-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.asset_templates (
  id text NOT NULL,
  name text NOT NULL,
  description text,
  asset_type text NOT NULL CHECK (asset_type = ANY (ARRAY['Badge'::text, 'Certificate'::text, 'Ticket'::text, 'Coupon'::text])),
  template_data jsonb NOT NULL DEFAULT '{}'::jsonb,
  preview_url text,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT asset_templates_pkey PRIMARY KEY (id)
);
CREATE TABLE public.asset_transfers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  asset_id uuid NOT NULL,
  from_odude_name text NOT NULL,
  from_email text NOT NULL,
  to_odude_name text NOT NULL,
  to_email text,
  status text NOT NULL DEFAULT 'pending'::text CHECK (status = ANY (ARRAY['pending'::text, 'approved'::text, 'declined'::text, 'hidden'::text])),
  transferred_at timestamp with time zone DEFAULT now(),
  responded_at timestamp with time zone,
  response_note text,
  CONSTRAINT asset_transfers_pkey PRIMARY KEY (id),
  CONSTRAINT asset_transfers_asset_id_fkey FOREIGN KEY (asset_id) REFERENCES public.assets(id)
);
CREATE TABLE public.assets (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text,
  asset_type text NOT NULL CHECK (asset_type = ANY (ARRAY['Badge'::text, 'Certificate'::text, 'Ticket'::text, 'Coupon'::text])),
  image_url text NOT NULL,
  template_id text,
  issuer_odude_name text NOT NULL,
  issuer_email text NOT NULL,
  expiry_date timestamp with time zone,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  is_deleted boolean DEFAULT false,
  CONSTRAINT assets_pkey PRIMARY KEY (id)
);
CREATE TABLE public.bookmark (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  contact_name character varying,
  contact_email character varying,
  CONSTRAINT bookmark_pkey PRIMARY KEY (id)
);
CREATE TABLE public.contact (
  timestamp timestamp with time zone DEFAULT now(),
  name text NOT NULL,
  image text,
  description text,
  uri text,
  profile text,
  email text,
  website text,
  phone text,
  tg_bot text,
  notes jsonb,
  web2 text,
  web3 text,
  links jsonb,
  images jsonb,
  social jsonb,
  crypto jsonb,
  extra jsonb,
  profile_email text,
  minted text,
  disabled boolean DEFAULT false,
  CONSTRAINT contact_pkey PRIMARY KEY (name)
);
CREATE TABLE public.primary_name_owners (
  id integer NOT NULL DEFAULT nextval('primary_name_owners_id_seq'::regclass),
  user_email text NOT NULL,
  owner_of text NOT NULL,
  ownership_type text DEFAULT 'static'::text CHECK (ownership_type = ANY (ARRAY['static'::text, 'dynamic'::text])),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT primary_name_owners_pkey PRIMARY KEY (id)
);
CREATE TABLE public.profiles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  email text NOT NULL UNIQUE,
  full_name text,
  avatar_url text,
  points integer DEFAULT 2000,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  disabled boolean NOT NULL DEFAULT false,
  CONSTRAINT profiles_pkey PRIMARY KEY (id)
);
CREATE TABLE public.settings (
  email text NOT NULL,
  max_contact_limit integer DEFAULT 4,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT settings_pkey PRIMARY KEY (email)
);
CREATE TABLE public.transaction_logs (
  id integer NOT NULL DEFAULT nextval('transaction_logs_id_seq'::regclass),
  email text NOT NULL,
  transaction_type text NOT NULL,
  points_change integer NOT NULL,
  points_before integer NOT NULL,
  points_after integer NOT NULL,
  description text,
  reference_id text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT transaction_logs_pkey PRIMARY KEY (id),
  CONSTRAINT fk_transaction_logs_email FOREIGN KEY (email) REFERENCES public.user_points(email)
);
CREATE TABLE public.user_points (
  email text NOT NULL,
  points integer NOT NULL DEFAULT 0 CHECK (points >= 0),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_points_pkey PRIMARY KEY (email)
);
CREATE TABLE public.user_qr_services (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  contact_name text NOT NULL,
  service_name text NOT NULL,
  service_data jsonb NOT NULL DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_qr_services_pkey PRIMARY KEY (id)
);