-- Sample Data for ODude Assets System Testing
-- This script inserts sample data to test the assets system functionality
-- Run this AFTER the main database setup script

-- ============================================================================
-- SAMPLE USER PROFILES
-- ============================================================================

-- Insert sample user profiles
INSERT INTO profiles (email, name, points) VALUES
('<EMAIL>', 'John Doe', 5000),
('<EMAIL>', 'Jane Smith', 3000),
('<EMAIL>', 'Admin User', 10000)
ON CONFLICT (email) DO UPDATE SET
  name = EXCLUDED.name,
  points = EXCLUDED.points;

-- ============================================================================
-- SAMPLE PRIMARY NAME OWNERSHIP
-- ============================================================================

-- Insert sample primary name ownership (required for asset creation)
INSERT INTO primary_name_owners (user_email, owner_of, ownership_type) VALUES
('<EMAIL>', 'company', 'static'),
('<EMAIL>', 'store', 'static'),
('<EMAIL>', 'me', 'static'),
('<EMAIL>', 'admin', 'static')
ON CONFLICT (user_email, owner_of) DO UPDATE SET
  ownership_type = EXCLUDED.ownership_type,
  updated_at = NOW();

-- ============================================================================
-- SAMPLE CONTACTS (ODude Names)
-- ============================================================================

-- Insert sample contacts
INSERT INTO contact (name, profile, description, profile_email, disabled) VALUES
('john@company', 'John Doe - CEO', 'Chief Executive Officer at Company Inc.', '<EMAIL>', false),
('jane@store', 'Jane Smith - Manager', 'Store Manager at Retail Store', '<EMAIL>', false),
('admin@me', 'Admin User', 'Platform Administrator', '<EMAIL>', false),
('support@admin', 'Support Team', 'Customer Support Team', '<EMAIL>', false)
ON CONFLICT (name) DO UPDATE SET
  profile = EXCLUDED.profile,
  description = EXCLUDED.description,
  profile_email = EXCLUDED.profile_email,
  disabled = EXCLUDED.disabled;

-- ============================================================================
-- SAMPLE ASSETS
-- ============================================================================

-- Insert sample assets (using placeholder image URLs)
INSERT INTO assets (
  title, 
  description, 
  asset_type, 
  image_url, 
  issuer_odude_name, 
  issuer_email,
  expiry_date,
  metadata
) VALUES
(
  'Employee of the Month Badge',
  'Recognition badge for outstanding performance in March 2024',
  'Badge',
  'https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=Employee+Badge',
  'john@company',
  '<EMAIL>',
  '2024-12-31T23:59:59Z',
  '{"achievement": "employee_of_month", "month": "March", "year": 2024}'
),
(
  'Professional Certificate',
  'Certificate of completion for Advanced Web Development Course',
  'Certificate',
  'https://via.placeholder.com/600x400/10B981/FFFFFF?text=Certificate',
  'jane@store',
  '<EMAIL>',
  '2025-12-31T23:59:59Z',
  '{"course": "Advanced Web Development", "grade": "A+", "hours": 120}'
),
(
  'Conference Ticket',
  'Access ticket for ODude Developer Conference 2024',
  'Ticket',
  'https://via.placeholder.com/500x300/F59E0B/FFFFFF?text=Conference+Ticket',
  'admin@me',
  '<EMAIL>',
  '2024-06-30T18:00:00Z',
  '{"event": "ODude Dev Conf 2024", "seat": "A-15", "access_level": "VIP"}'
),
(
  '20% Discount Coupon',
  'Special discount coupon for new customers',
  'Coupon',
  'https://via.placeholder.com/400x250/EF4444/FFFFFF?text=20%25+OFF',
  'jane@store',
  '<EMAIL>',
  '2024-12-31T23:59:59Z',
  '{"discount_percent": 20, "min_purchase": 50, "category": "electronics"}'
);

-- ============================================================================
-- SAMPLE ASSET TRANSFERS
-- ============================================================================

-- Insert sample asset transfers
INSERT INTO asset_transfers (
  asset_id,
  from_odude_name,
  from_email,
  to_odude_name,
  to_email,
  status,
  response_note
) VALUES
(
  (SELECT id FROM assets WHERE title = 'Employee of the Month Badge' LIMIT 1),
  'john@company',
  '<EMAIL>',
  'jane@store',
  '<EMAIL>',
  'approved',
  'Thank you for this recognition!'
),
(
  (SELECT id FROM assets WHERE title = 'Conference Ticket' LIMIT 1),
  'admin@me',
  '<EMAIL>',
  'john@company',
  '<EMAIL>',
  'pending',
  NULL
),
(
  (SELECT id FROM assets WHERE title = '20% Discount Coupon' LIMIT 1),
  'jane@store',
  '<EMAIL>',
  'admin@me',
  '<EMAIL>',
  'declined',
  'Already have similar discount'
);

-- ============================================================================
-- SAMPLE TRANSACTION LOG
-- ============================================================================

-- Insert sample transaction log entries
INSERT INTO transaction_log (
  user_email,
  transaction_type,
  points,
  from_user,
  to_user,
  description
) VALUES
('<EMAIL>', 'SIGNUP', 2000, NULL, '<EMAIL>', 'Welcome signup bonus'),
('<EMAIL>', 'SIGNUP', 2000, NULL, '<EMAIL>', 'Welcome signup bonus'),
('<EMAIL>', 'SIGNUP', 2000, NULL, '<EMAIL>', 'Welcome signup bonus'),
('<EMAIL>', 'CREATE_CONTACT', -1000, '<EMAIL>', NULL, 'Created contact: john@company'),
('<EMAIL>', 'CREATE_CONTACT', -1000, '<EMAIL>', NULL, 'Created contact: jane@store'),
('<EMAIL>', 'CREATE_CONTACT', -1000, '<EMAIL>', NULL, 'Created contact: admin@me'),
('<EMAIL>', 'CREATE_CONTACT', -1000, '<EMAIL>', NULL, 'Created contact: support@admin');

-- ============================================================================
-- SAMPLE QR SERVICES
-- ============================================================================

-- Insert sample QR services
INSERT INTO user_qr_services (contact_name, service_name, service_data, is_active) VALUES
('john@company', 'email', '{"email": "<EMAIL>", "subject": "Business Inquiry"}', true),
('john@company', 'phone', '{"phone": "******-0123"}', true),
('jane@store', 'email', '{"email": "<EMAIL>", "subject": "Store Inquiry"}', true),
('jane@store', 'website', '{"url": "https://store.example.com"}', true),
('admin@me', 'email', '{"email": "<EMAIL>", "subject": "Platform Support"}', true)
ON CONFLICT (contact_name, service_name) DO UPDATE SET
  service_data = EXCLUDED.service_data,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Show sample data summary
SELECT 'Sample Data Insertion Complete!' as message;

SELECT 'User Profiles:' as section, COUNT(*) as count FROM profiles
UNION ALL
SELECT 'Primary Name Owners:', COUNT(*) FROM primary_name_owners
UNION ALL
SELECT 'Contacts:', COUNT(*) FROM contact
UNION ALL
SELECT 'Assets:', COUNT(*) FROM assets
UNION ALL
SELECT 'Asset Transfers:', COUNT(*) FROM asset_transfers
UNION ALL
SELECT 'Transaction Log:', COUNT(*) FROM transaction_log
UNION ALL
SELECT 'QR Services:', COUNT(*) FROM user_qr_services;

-- Show assets with their transfer status
SELECT 
  a.title,
  a.asset_type,
  a.issuer_odude_name,
  COUNT(at.id) as total_transfers,
  COUNT(CASE WHEN at.status = 'pending' THEN 1 END) as pending_transfers,
  COUNT(CASE WHEN at.status = 'approved' THEN 1 END) as approved_transfers,
  COUNT(CASE WHEN at.status = 'declined' THEN 1 END) as declined_transfers
FROM assets a
LEFT JOIN asset_transfers at ON a.id = at.asset_id
GROUP BY a.id, a.title, a.asset_type, a.issuer_odude_name
ORDER BY a.created_at;

-- Show user points balances
SELECT 
  email,
  name,
  points,
  (SELECT COUNT(*) FROM primary_name_owners WHERE user_email = p.email) as owned_primary_names
FROM profiles p
ORDER BY points DESC;
