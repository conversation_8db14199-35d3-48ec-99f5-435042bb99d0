import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';
import { ADMIN_EMAIL, FTP_HOST, FTP_USER, FTP_PASSWORD } from 'src/lib/config';
import { verifyAdminAuth } from 'src/lib/adminAuth';
import { auth } from 'auth';

interface SystemCheck {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: string;
}

/**
 * Check database tables
 */
async function checkDatabaseTables(): Promise<{ tables: string[]; missing: string[] }> {
  const requiredTables = ['contact', 'bookmark', 'profiles', 'settings', 'user_points', 'transaction_logs'];
  const supabase = getSupabaseAdminClient();
  const existingTables: string[] = [];

  // Check each table individually by trying to query it
  for (const table of requiredTables) {
    try {
      const { error } = await supabase
        .from(table)
        .select('*')
        .limit(1);

      if (!error) {
        existingTables.push(table);
      }
    } catch (error) {
      // Table doesn't exist or can't be accessed
      console.log(`Table ${table} not accessible:`, error);
    }
  }

  const missingTables = requiredTables.filter(table => !existingTables.includes(table));
  return { tables: existingTables, missing: missingTables };
}

/**
 * Check database functions
 */
async function checkDatabaseFunctions(): Promise<{ functions: string[]; missing: string[] }> {
  const requiredFunctions = ['update_user_points', 'transfer_points'];
  const existingFunctions: string[] = [];

  const supabase = getSupabaseAdminClient();

  // Test update_user_points function
  try {
    // Try to call with invalid parameters to see if function exists
    const { error } = await supabase.rpc('update_user_points', {
      user_email: '<EMAIL>',
      points_change: 0,
      transaction_type: 'TEST'
    });

    // If we get any response (even an error), the function exists
    existingFunctions.push('update_user_points');
  } catch (error) {
    console.log('update_user_points function not found');
  }

  // Test transfer_points function
  try {
    // Try to call with invalid parameters to see if function exists
    const { error } = await supabase.rpc('transfer_points', {
      sender_email: '<EMAIL>',
      recipient_email: '<EMAIL>',
      points_amount: 0
    });

    // If we get any response (even an error), the function exists
    existingFunctions.push('transfer_points');
  } catch (error) {
    console.log('transfer_points function not found');
  }

  const missingFunctions = requiredFunctions.filter(func => !existingFunctions.includes(func));
  return { functions: existingFunctions, missing: missingFunctions };
}

/**
 * Check configuration status
 */
async function checkConfiguration(): Promise<SystemCheck[]> {
  const checks: SystemCheck[] = [];

  // Check environment variables
  checks.push({
    name: 'Database Connection',
    status: process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'pass' : 'fail',
    message: process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Database credentials configured' : 'Missing database credentials',
    details: 'NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY required'
  });

  checks.push({
    name: 'FTP Configuration',
    status: FTP_HOST && FTP_USER && FTP_PASSWORD ? 'pass' : 'warning',
    message: FTP_HOST && FTP_USER && FTP_PASSWORD ? 'FTP backup configured' : 'FTP backup not configured',
    details: 'Required for backup functionality'
  });

  checks.push({
    name: 'Admin Email',
    status: ADMIN_EMAIL ? 'pass' : 'fail',
    message: ADMIN_EMAIL ? `Admin email set to ${ADMIN_EMAIL}` : 'Admin email not configured',
    details: 'Required for admin access'
  });

  // Check NextAuth by testing if we can get the current session
  let nextAuthStatus: 'pass' | 'fail' | 'warning' = 'fail';
  let nextAuthMessage = 'NextAuth not properly configured';
  try {
    const session = await auth();
    // If we can call auth() without error, NextAuth is configured
    nextAuthStatus = 'pass';
    nextAuthMessage = 'NextAuth configured and working';
  } catch (error) {
    console.log('NextAuth check error:', error);
  }

  checks.push({
    name: 'NextAuth Configuration',
    status: nextAuthStatus,
    message: nextAuthMessage,
    details: 'NEXTAUTH_SECRET and NEXTAUTH_URL required'
  });

  checks.push({
    name: 'Google OAuth',
    status: process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? 'pass' : 'warning',
    message: process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? 'Google OAuth configured' : 'Google OAuth not configured',
    details: 'Required for Google sign-in'
  });

  return checks;
}

/**
 * Perform system health checks
 */
async function performSystemChecks(): Promise<SystemCheck[]> {
  const checks: SystemCheck[] = [];
  const supabase = getSupabaseAdminClient();

  // Test database connection
  try {
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    checks.push({
      name: 'Database Connection',
      status: error ? 'fail' : 'pass',
      message: error ? 'Database connection failed' : 'Database connection successful',
      details: error ? error.message : undefined
    });
  } catch (error) {
    checks.push({
      name: 'Database Connection',
      status: 'fail',
      message: 'Database connection failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }

  // Point System Functions check removed as requested

  // Check if required tables have data structure
  try {
    const { data: contactData, error: contactError } = await supabase.from('contact').select('*').limit(1);
    const { data: pointsData, error: pointsError } = await supabase.from('user_points').select('*').limit(1);

    checks.push({
      name: 'Table Structure',
      status: contactError || pointsError ? 'warning' : 'pass',
      message: contactError || pointsError ? 'Some tables may have structure issues' : 'All tables accessible',
      details: contactError ? `Contact table: ${contactError.message}` : pointsError ? `Points table: ${pointsError.message}` : undefined
    });
  } catch (error) {
    checks.push({
      name: 'Table Structure',
      status: 'fail',
      message: 'Cannot access required tables',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }

  return checks;
}

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can access system info
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    // Get system information
    const [tableInfo, functionInfo, systemChecks, configStatus] = await Promise.all([
      checkDatabaseTables(),
      checkDatabaseFunctions(),
      performSystemChecks(),
      checkConfiguration()
    ]);

    const systemInfo = {
      nodeVersion: process.version,
      nextVersion: process.env.npm_package_dependencies_next || 'Unknown',
      databaseTables: tableInfo.tables,
      databaseFunctions: functionInfo.functions,
      missingTables: tableInfo.missing,
      missingFunctions: functionInfo.missing,
      configStatus,
      systemChecks,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(systemInfo);

  } catch (error) {
    console.error('System info API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
